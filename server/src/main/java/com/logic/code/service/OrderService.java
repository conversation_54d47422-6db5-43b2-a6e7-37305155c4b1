package com.logic.code.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.plugins.pagination.PageDTO;
import com.logic.code.common.enmus.OrderStatusEnum;
import com.logic.code.common.enmus.PayStatusEnum;
import com.logic.code.common.enmus.WeshopWechatResultStatus;
import com.logic.code.common.exception.WeshopWechatException;
import com.logic.code.common.utils.IdGenerator;
import com.logic.code.common.utils.JwtHelper;
import com.logic.code.entity.*;
import com.logic.code.entity.order.Cart;
import com.logic.code.entity.order.Order;
import com.logic.code.entity.order.OrderExpress;
import com.logic.code.entity.order.OrderGoods;
import com.logic.code.mapper.CommonMapper;
import com.logic.code.mapper.OrderMapper;
import com.logic.code.mapper.UserCouponMapper;
import com.logic.code.mapper.UserMapper;
import com.logic.code.model.query.OrderQuery;
import com.logic.code.model.vo.*;
import com.logic.code.model.vo.PurchaseLimitVO;
import io.jsonwebtoken.Claims;
import jakarta.annotation.Resource;
import logic.orm.WrapperBuilder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2025/5/7 22:59
 * @desc
 */
@Service
@Slf4j
public class OrderService extends BaseService<Order> {

    @Resource
    private OrderMapper orderMapper;

    @Override
    protected CommonMapper<Order> getMapper() {
        return orderMapper;
    }


    @Resource
    private OrderGoodsService orderGoodsService;

    @Resource
    private OrderExpressService orderExpressService;

    @Resource
    private CartService cartService;

    @Resource
    private AddressService addressService;

    @Resource
    private RegionService regionService;

    @Resource
    private CardService cardService;

    @Resource
    private UserService userService;

    @Resource
    private WxMessageService wxMessageService;

    @Resource
    private AdminNotificationService adminNotificationService;

    @Resource
    private PointsService pointsService;

    @Resource
    private UserCouponService userCouponService;

    @Resource
    private UserCouponMapper userCouponMapper;

    @Resource
    private BalanceService balanceService;

    @Resource
    private PromotionEarningsService promotionEarningsService;

    @Resource
    private FreightService freightService;

    @Resource
    private TieredPromotionService tieredPromotionService;

    @Resource
    private PurchaseLimitService purchaseLimitService;

    @Resource
    private UserMapper userMapper;

    /**
     * 根据商户订单号查询订单
     *
     * @param outTradeNo 商户订单号
     * @return 订单信息
     */
    public Order getByOutTradeNo(String outTradeNo) {
        if (outTradeNo == null || outTradeNo.isEmpty()) {
            return null;
        }
        Criteria<Order, Object> criteria = Criteria.of(Order.class)
                .andEqualTo(Order::getOrderSn, outTradeNo);
        return queryOneByCriteria(criteria);
    }

    public Order queryByCardId(Integer cardId) {
        Criteria<Order, Object> criteria = Criteria.of(Order.class).andEqualTo(Order::getCouponId, cardId);
        Order order = queryOneByCriteria(criteria);
        return order;
    }

    /**
     * 查询所有订单（管理员使用）
     */
    public List<Order> queryAll() {
        Criteria<Order, Object> criteria = Criteria.of(Order.class);
        return queryByCriteria(criteria);
    }

    public List<OrderListVO> queryOrderList(OrderQuery orderQuery) {
        User userInfo = JwtHelper.getUserInfo();
        Integer userId = userInfo.getId();
        if(orderQuery.getUserId()!=null){
            userId = orderQuery.getUserId();
        }
        Criteria<Order, Object> criteria = Criteria.of(Order.class).andEqualTo(Order::getUserId, userId).page(orderQuery.getPageNum(), orderQuery.getPageSize());
        if (orderQuery.getOrderStatus() != null) {
            criteria.andEqualTo(Order::getOrderStatus, orderQuery.getOrderStatus());
        }
        criteria.sortDesc(Order::getCreateTime);
        List<Order> orderList = queryByCriteria(criteria);
        List<OrderListVO> orderVOList = new LinkedList<>();
        for (Order order : orderList) {
            OrderListVO orderVO = new OrderListVO(order)
                    .setGoodsList(orderGoodsService.queryList(OrderGoods.builder().orderId(order.getId()).build()))
                    .setHandleOption(new HandleOptionVO(order))
                    .setOrderStatusText(order.getOrderStatus().getName());
            orderVOList.add(orderVO);
        }
        return orderVOList;
    }


    public Page getOrderList(OrderQuery orderQuery) {
        // 使用传入的分页参数，如果没有传入则使用默认值
        int pageNum = orderQuery.getPageNum() > 0 ? orderQuery.getPageNum() : 1;
        int pageSize = orderQuery.getPageSize() > 0 ? orderQuery.getPageSize() : 10;
        
        Page page = PageDTO.of(pageNum, pageSize);
        
        // 构建查询条件
        QueryWrapper<Order> wrapper = new QueryWrapper<>();
        if (orderQuery.getOrderStatus() != null) {
            wrapper.eq("order_status", orderQuery.getOrderStatus());
        }
        // 按创建时间倒序排列
        wrapper.orderByDesc("create_time");
        
        Page selectPage = orderMapper.selectPage(page, wrapper);
        List<Order> orderList = selectPage.getRecords();
        List<OrderListVO> orderVOList = new LinkedList<>();
        for (Order order : orderList) {
            OrderListVO orderVO = new OrderListVO(order)
                    .setGoodsList(orderGoodsService.queryList(OrderGoods.builder().orderId(order.getId()).build()))
                    .setHandleOption(new HandleOptionVO(order))
                    .setOrderStatusText(order.getOrderStatus().getName());
            if (order.getCouponId() != null && order.getCouponId() > 0) {
                orderVO.setOrderTypeName("卡劵兑换");
            }
            orderVOList.add(orderVO);

        }
        selectPage.setRecords(orderVOList);
        return selectPage;
    }

    public OrderDetailVO queryOrderDetail(Integer orderId) {
        Order order = Optional.ofNullable(queryById(orderId))
                .orElseThrow(() -> new WeshopWechatException(WeshopWechatResultStatus.ORDER_NOT_EXIST));

        OrderDetailVO.OrderInfoVO orderInfoVO = new OrderDetailVO.OrderInfoVO(order).setOrderExpress(orderExpressService.queryByOrderId(148));

        orderInfoVO.setProvinceName(
                regionService.queryNameById(orderInfoVO.getProvince())
        ).setCityName(
                regionService.queryNameById(orderInfoVO.getCity())
        ).setDistrictName(
                regionService.queryNameById(orderInfoVO.getDistrict())
        );
        orderInfoVO.setFullRegion(orderInfoVO.getProvinceName() + orderInfoVO.getCityName() + orderInfoVO.getDistrictName());

        // 获取优惠券历史信息
        if (order.getCouponId() != null && order.getCouponPrice() != null && order.getCouponPrice().compareTo(BigDecimal.ZERO) > 0) {
            // 通过lastUsedOrderId查询优惠券信息
            QueryWrapper<UserCoupon> wrapper = new QueryWrapper<>();
            wrapper.eq("last_used_order_id", orderId);

            UserCoupon coupon = userCouponMapper.selectOne(wrapper);
            if (coupon != null) {
                orderInfoVO.setCouponTitle(coupon.getTitle());
                orderInfoVO.setCouponDescription(coupon.getDescription());
                orderInfoVO.setCouponRefunded(coupon.getIsRefunded());

                log.info("订单{}优惠券信息：标题={}，已退回={}", orderId, coupon.getTitle(), coupon.getIsRefunded());
            }
        }

        List<OrderGoods> orderGoodsList = orderGoodsService.queryList(OrderGoods.builder().orderId(orderId).build());

        return new OrderDetailVO(orderInfoVO, orderGoodsList, new HandleOptionVO(order));
    }

    /**
     * 发送订单通知消息
     * 注意：由于微信客服消息的限制，用户需要在48小时内与小程序有交互，否则无法发送消息
     *
     * @param order          订单信息
     * @param userInfo       用户基本信息
     * @param orderGoodsList 订单商品列表
     */
    private void sendOrderNotification(Order order, User userInfo, List<OrderGoods> orderGoodsList) {
        try {
            // 获取完整的用户信息，确保包含微信OpenID
            User user = userService.queryById(userInfo.getId());
            if (user == null || user.getWechatOpenId() == null || user.getWechatOpenId().isEmpty()) {
                log.warn("用户信息不完整或无微信OpenID，无法发送通知，userId: {}, orderId: {}",
                        userInfo.getId(), order.getId());
                return;
            }

            // 确定是否为卡券订单
            boolean isCardOrder = order.getCouponId() != null && order.getCouponId() > 0;

            // 发送订单通知
            boolean success = wxMessageService.sendOrderNotice(order, user, orderGoodsList, isCardOrder);
            if (success) {
                log.info("订单通知发送成功，订单ID: {}, 用户ID: {}", order.getId(), user.getId());
            } else {
                // 可能是由于48小时限制导致的发送失败
                log.warn("订单通知发送失败，可能是由于48小时限制，订单ID: {}, 用户ID: {}", order.getId(), user.getId());

                // 在这里可以添加其他通知方式，如短信、邮件等，或将通知保存到用户消息中心
                // saveToUserMessageCenter(order, user, "您有一个新的订单已创建成功");
            }
        } catch (Exception e) {
            // 消息发送失败不影响订单创建，只记录日志
            log.error("发送订单通知失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 提交普通订单
     */
    public OrderSubmitResultVO submitOrder(OrderSubmitParamVO orderSubmitParamDTO) {
        User userInfo = JwtHelper.getUserInfo();
        Claims currentClaims = JwtHelper.getCurrentClaims();
        Address checkedAddress = addressService.queryById(orderSubmitParamDTO.getAddressId());
        if (checkedAddress == null) {
            throw new WeshopWechatException(WeshopWechatResultStatus.PLEASE_SELECT_SHIPPING_ADDRESS);
        }

        boolean isEmptyCart = false;

        //获取要购买的商品
        List<Cart> checkedGoodsList = cartService.queryList(
                new Cart()
                        .setUserId(userInfo.getId())
                        .setSessionId(currentClaims.getId())
                        .setChecked(true)
        );
        if (checkedGoodsList.isEmpty()) {
            isEmptyCart = true;
            CartParamVO cartParamDTO = new CartParamVO();
            cartParamDTO.setGoodsId(orderSubmitParamDTO.getGoodsId());
            cartParamDTO.setProductId(orderSubmitParamDTO.getProductId());
            cartParamDTO.setNumber(orderSubmitParamDTO.getNumber());
            checkedGoodsList = cartService.packageGoodsToCart(cartParamDTO);
            //throw new WeshopWechatException(WeshopWechatResultStatus.PLEASE_SELECT_GOODS);
        }

        // 限购检查
        for (Cart cart : checkedGoodsList) {
            boolean isValidPurchase = purchaseLimitService.validatePurchaseCount(
                    cart.getGoodsId(),
                    userInfo.getId(),
                    cart.getNumber()
            );

            if (!isValidPurchase) {
                PurchaseLimitVO limitInfo = purchaseLimitService.checkPurchaseLimit(
                        cart.getGoodsId(),
                        userInfo.getId()
                );
                log.warn("用户{}购买商品{}超出限购数量，商品名：{}，限购：{}件，已购：{}件，本次购买：{}件",
                        userInfo.getId(), cart.getGoodsId(), cart.getGoodsName(),
                        limitInfo.getLimitNum(), limitInfo.getPurchasedCount(), cart.getNumber());
                throw new WeshopWechatException(WeshopWechatResultStatus.PURCHASE_LIMIT_EXCEEDED);
            }
        }

        //统计商品总价
        BigDecimal goodsTotalPrice = BigDecimal.ZERO;
        for (Cart cart : checkedGoodsList) {
            goodsTotalPrice = goodsTotalPrice.add(
                    cart.getRetailPrice().multiply(new BigDecimal(cart.getNumber()))
            );
        }

        //运费价格
        BigDecimal freightPrice = freightService.calculateFreight(checkedAddress, goodsTotalPrice);

        //获取订单使用的优惠券
        BigDecimal couponPrice = BigDecimal.ZERO;
        if (orderSubmitParamDTO.getCouponId() != null && orderSubmitParamDTO.getCouponId() > 0) {
            // 获取用户优惠券信息
            List<UserCoupon> userCoupons = userCouponService.getUserCoupons(userInfo.getId(), "available", 1, 100);
            UserCoupon selectedCoupon = userCoupons.stream()
                    .filter(coupon -> coupon.getId().equals(orderSubmitParamDTO.getCouponId()))
                    .findFirst()
                    .orElse(null);

            if (selectedCoupon != null) {
                // 检查优惠券是否满足使用条件
                if (selectedCoupon.getMinAmount().compareTo(goodsTotalPrice) <= 0) {
                    couponPrice = selectedCoupon.getAmount();
                    log.info("订单使用优惠券：{}, 抵扣金额：{}", selectedCoupon.getTitle(), couponPrice);
                } else {
                    log.warn("优惠券{}不满足使用条件，最低消费：{}，订单金额：{}",
                            selectedCoupon.getTitle(), selectedCoupon.getMinAmount(), goodsTotalPrice);
                    throw new WeshopWechatException(WeshopWechatResultStatus.COUPON_USE_FAILED);
                }
            } else {
                log.warn("未找到可用的优惠券，ID：{}", orderSubmitParamDTO.getCouponId());
                throw new WeshopWechatException(WeshopWechatResultStatus.COUPON_USE_FAILED);
            }
        }

        // 计算积分抵扣
        Integer usePoints = orderSubmitParamDTO.getUsePoints();
        BigDecimal pointsPrice = BigDecimal.ZERO;
        if (usePoints != null && usePoints > 0) {
            // 验证用户积分是否足够（使用实时计算的积分）
            Integer userPoints = pointsService.getUserRealTimePoints(userInfo.getId());
            if (userPoints < usePoints) {
                log.warn("用户{}积分不足：当前积分={}, 尝试使用={}", userInfo.getId(), userPoints, usePoints);
                throw new WeshopWechatException(WeshopWechatResultStatus.INSUFFICIENT_POINTS);
            }
            log.debug("用户{}积分验证通过：当前积分={}, 使用积分={}", userInfo.getId(), userPoints, usePoints);

            // 计算积分可抵扣的金额
            pointsPrice = pointsService.calculatePointsValue(usePoints);
        }

        // 计算余额使用
        BigDecimal useBalance = orderSubmitParamDTO.getUseBalance();
        BigDecimal balancePrice = BigDecimal.ZERO;
        if (useBalance != null && useBalance.compareTo(BigDecimal.ZERO) > 0) {
            // 验证用户余额是否足够
            User currentUser = userService.queryById(userInfo.getId());
            BigDecimal userBalance = currentUser.getBalance() != null ? currentUser.getBalance() : BigDecimal.ZERO;
            if (userBalance.compareTo(useBalance) < 0) {
                throw new WeshopWechatException(WeshopWechatResultStatus.INSUFFICIENT_BALANCE);
            }
            balancePrice = useBalance;
        }

        // 验证总抵扣金额不能超过订单金额
        BigDecimal orderBaseAmount = goodsTotalPrice.add(freightPrice);
        BigDecimal totalDeduction = couponPrice.add(pointsPrice).add(balancePrice);

        if (totalDeduction.compareTo(orderBaseAmount) > 0) {
            log.warn("订单总抵扣金额{}超过订单基础金额{}，进行调整", totalDeduction, orderBaseAmount);

            // 按优先级调整抵扣金额：优惠券 > 积分 > 余额
            BigDecimal remainingAmount = orderBaseAmount;

            // 优惠券抵扣（优先级最高，不调整）
            remainingAmount = remainingAmount.subtract(couponPrice);

            // 积分抵扣调整
            if (pointsPrice.compareTo(remainingAmount) > 0) {
                BigDecimal originalPointsPrice = pointsPrice;
                pointsPrice = remainingAmount.max(BigDecimal.ZERO);
                remainingAmount = BigDecimal.ZERO;
                log.info("积分抵扣金额从{}调整为{}", originalPointsPrice, pointsPrice);
            } else {
                remainingAmount = remainingAmount.subtract(pointsPrice);
            }

            // 余额抵扣调整
            if (balancePrice.compareTo(remainingAmount) > 0) {
                BigDecimal originalBalancePrice = balancePrice;
                balancePrice = remainingAmount.max(BigDecimal.ZERO);
                log.info("余额抵扣金额从{}调整为{}", originalBalancePrice, balancePrice);
            }

            log.info("调整后的抵扣金额：优惠券={}, 积分={}, 余额={}", couponPrice, pointsPrice, balancePrice);
        }

        // 订单价格计算  实际价格 = 商品价格 + 运费价格 - 优惠券价格 - 积分抵扣 - 余额抵扣
        BigDecimal orderTotalPrice = goodsTotalPrice.add(freightPrice).subtract(couponPrice);
        BigDecimal actualPrice = orderTotalPrice.subtract(pointsPrice).subtract(balancePrice);

        // 确保实际支付金额不为负数
        if (actualPrice.compareTo(BigDecimal.ZERO) < 0) {
            actualPrice = BigDecimal.ZERO;
        }
        Date currentTime = new Date();

        Order orderInfo = new Order();
        orderInfo.setOrderSn(IdGenerator.INSTANCE.nextId());
        orderInfo.setUserId(userInfo.getId());

        //收货地址和运费
        orderInfo.setConsignee(checkedAddress.getName());
        orderInfo.setMobile(checkedAddress.getMobile());
        orderInfo.setProvince(checkedAddress.getProvinceId());
        orderInfo.setCity(checkedAddress.getCityId());
        orderInfo.setDistrict(checkedAddress.getDistrictId());
        orderInfo.setAddress(checkedAddress.getAddress());
        orderInfo.setFreightPrice(freightPrice);

        //留言
        orderInfo.setPostscript(orderSubmitParamDTO.getPostscript());

        //使用优惠券
        orderInfo.setCouponId(orderSubmitParamDTO.getCouponId() != null ? orderSubmitParamDTO.getCouponId() : 0);
        orderInfo.setCouponPrice(couponPrice);

        //使用积分
        orderInfo.setIntegral(usePoints != null ? usePoints : 0);
        orderInfo.setIntegralMoney(pointsPrice);

        //使用余额
        orderInfo.setBalancePrice(balancePrice);

        // 记录组合抵扣信息
        int discountCount = 0;
        StringBuilder discountInfo = new StringBuilder("订单抵扣详情：");

        if (couponPrice.compareTo(BigDecimal.ZERO) > 0) {
            discountCount++;
            discountInfo.append("优惠券").append(couponPrice).append("元 ");
        }
        if (pointsPrice.compareTo(BigDecimal.ZERO) > 0) {
            discountCount++;
            discountInfo.append("积分").append(pointsPrice).append("元(").append(usePoints).append("积分) ");
        }
        if (balancePrice.compareTo(BigDecimal.ZERO) > 0) {
            discountCount++;
            discountInfo.append("余额").append(balancePrice).append("元 ");
        }

        if (discountCount > 1) {
            BigDecimal totalDiscount = couponPrice.add(pointsPrice).add(balancePrice);
            log.info("用户{}使用组合抵扣：{}种方式，总计节省{}元。{}",
                    userInfo.getId(), discountCount, totalDiscount, discountInfo.toString());
        } else if (discountCount == 1) {
            log.info("用户{}使用单一抵扣：{}", userInfo.getId(), discountInfo.toString());
        }

        orderInfo.setCreateTime(currentTime);
        orderInfo.setGoodsPrice(goodsTotalPrice);
        orderInfo.setOrderPrice(orderTotalPrice);
        orderInfo.setActualPrice(actualPrice);

        // 根据实际支付金额设置订单状态
        if (actualPrice.compareTo(BigDecimal.ZERO) <= 0) {
            // 0元订单直接设置为已支付状态，并设置支付时间
            orderInfo.setOrderStatus(OrderStatusEnum.WAIT_SEND);
            orderInfo.setPayStatus(PayStatusEnum.PAID);
            orderInfo.setPayTime(currentTime);  // 设置支付时间为当前时间
            log.info("创建0元订单，直接设置为已支付状态，订单号：{}，支付时间：{}", orderInfo.getOrderSn(), currentTime);
        } else {
            // 正常订单设置为待支付状态
            orderInfo.setOrderStatus(OrderStatusEnum.WAIT_PAY);
            orderInfo.setPayStatus(PayStatusEnum.PENDING_PAYMENT);
        }


        try {
            create(orderInfo);
        } catch (Exception e) {
            throw new WeshopWechatException(WeshopWechatResultStatus.CREATE_ORDER_ERROR);
        }


        Order order = queryOneByCriteria(Criteria.of(Order.class).andEqualTo(Order::getOrderSn, orderInfo.getOrderSn()));
        if (order == null) {

            throw new WeshopWechatException(WeshopWechatResultStatus.CREATE_ORDER_ERROR);
        }

        //统计商品总价
        List<OrderGoods> orderGoodsList = new LinkedList<>();
        for (Cart goodsItem : checkedGoodsList) {
            OrderGoods orderGoods = new OrderGoods();
            orderGoods.setOrderId(order.getId());
            orderGoods.setGoodsId(goodsItem.getGoodsId());
            orderGoods.setGoodsSn(goodsItem.getGoodsSn());
            orderGoods.setProductId(goodsItem.getProductId());
            orderGoods.setGoodsName(goodsItem.getGoodsName());
            orderGoods.setListPicUrl(goodsItem.getListPicUrl());
            orderGoods.setMarketPrice(goodsItem.getMarketPrice());
            orderGoods.setRetailPrice(goodsItem.getRetailPrice());
            orderGoods.setNumber(goodsItem.getNumber());
            orderGoods.setGoodsSpecificationNameValue(goodsItem.getGoodsSpecificationNameValue());
            orderGoods.setGoodsSpecificationIds(goodsItem.getGoodsSpecificationIds());
            orderGoods.setIsReal(true);
            orderGoodsList.add(orderGoods);
        }
        try {
            orderGoodsService.createBatch(orderGoodsList);
        } catch (Exception e) {
            throw new WeshopWechatException(WeshopWechatResultStatus.CREATE_ORDER_ERROR);
        }

        // 订单商品创建完成后，设置推广者信息（支持阶梯推广）
        setPromotionInfoWithGoods(order, userInfo, orderGoodsList, orderSubmitParamDTO.getPromoterId());

        // 使用优惠券
        if (orderSubmitParamDTO.getCouponId() != null && orderSubmitParamDTO.getCouponId() > 0) {
            boolean couponUsed = userCouponService.useCoupon(
                    userInfo.getId(),
                    orderSubmitParamDTO.getCouponId(),
                    order.getId()
            );
            if (!couponUsed) {
                throw new WeshopWechatException(WeshopWechatResultStatus.COUPON_USE_FAILED);
            }
            log.info("订单{}成功使用优惠券{}", order.getId(), orderSubmitParamDTO.getCouponId());
        }

        // 使用积分抵扣
        if (usePoints != null && usePoints > 0) {
            try {
                boolean pointsUsed = pointsService.usePointsForOrder(
                        userInfo.getId(),
                        order.getId(),
                        usePoints,
                        "订单使用积分抵扣"
                );
                if (!pointsUsed) {
                    throw new WeshopWechatException(WeshopWechatResultStatus.POINTS_USE_FAILED);
                }
            } catch (RuntimeException e) {
                log.error("订单{}积分扣减失败：{}", order.getId(), e.getMessage());
                throw new WeshopWechatException(WeshopWechatResultStatus.POINTS_USE_FAILED);
            }
        }

        // 使用余额支付
        if (balancePrice.compareTo(BigDecimal.ZERO) > 0) {
            boolean balanceUsed = balanceService.useBalanceForOrder(
                    userInfo.getId(),
                    balancePrice,
                    order.getId(),
                    "订单使用余额支付"
            );
            if (!balanceUsed) {
                throw new WeshopWechatException(WeshopWechatResultStatus.BALANCE_USE_FAILED);
            }
            log.info("订单{}成功使用余额{}", order.getId(), balancePrice);
        }

//        清空购物车已购买商品
        if (!isEmptyCart)
            cartService.delete(new Cart().setUserId(userInfo.getId()).setSessionId(currentClaims.getId()).setChecked(true));

        // 添加订单通知
        try {
            // 发送微信订阅消息通知
            sendOrderSubscribeNotification(order, userInfo, orderGoodsList);

            // 向运维人员发送订单通知
            sendAdminNotification(order, userInfo, orderGoodsList, false);
        } catch (Exception e) {
            log.error("发送普通订单通知失败，但不影响订单创建: {}", e.getMessage());
        }

        return new OrderSubmitResultVO(order);
    }

    @Transactional
    public OrderSubmitResultVO submitCardOrder(OrderSubmitParamVO param) {
        User userInfo = JwtHelper.getUserInfo();
        Claims currentClaims = JwtHelper.getCurrentClaims();
        Address checkedAddress = addressService.queryById(param.getAddressId());
        if (checkedAddress == null) {
            throw new WeshopWechatException(WeshopWechatResultStatus.PLEASE_SELECT_SHIPPING_ADDRESS);
        }

        // 获取要购买的商品
        CartParamVO cartParamDTO = new CartParamVO();
        cartParamDTO.setGoodsId(param.getGoodsId());
        cartParamDTO.setProductId(param.getProductId());
        cartParamDTO.setNumber(param.getNumber());

        List<Cart> checkedGoodsList = cartService.packageGoodsToCart(cartParamDTO);

        if (checkedGoodsList.isEmpty()) {
            throw new WeshopWechatException(WeshopWechatResultStatus.PLEASE_SELECT_GOODS);
        }

        // 限购检查
        for (Cart cart : checkedGoodsList) {
            boolean isValidPurchase = purchaseLimitService.validatePurchaseCount(
                    cart.getGoodsId(),
                    userInfo.getId(),
                    cart.getNumber()
            );

            if (!isValidPurchase) {
                PurchaseLimitVO limitInfo = purchaseLimitService.checkPurchaseLimit(
                        cart.getGoodsId(),
                        userInfo.getId()
                );
                log.warn("用户{}购买商品{}超出限购数量，商品名：{}，限购：{}件，已购：{}件，本次购买：{}件",
                        userInfo.getId(), cart.getGoodsId(), cart.getGoodsName(),
                        limitInfo.getLimitNum(), limitInfo.getPurchasedCount(), cart.getNumber());
                throw new WeshopWechatException(WeshopWechatResultStatus.PURCHASE_LIMIT_EXCEEDED);
            }
        }

        // 统计商品总价
        BigDecimal goodsTotalPrice = BigDecimal.ZERO;
        for (Cart cart : checkedGoodsList) {
            goodsTotalPrice = goodsTotalPrice.add(
                    cart.getRetailPrice().multiply(new BigDecimal(cart.getNumber()))
            );
        }

        // 运费价格
        BigDecimal freightPrice = freightService.calculateFreight(checkedAddress, goodsTotalPrice);

        // 获取订单使用的优惠券
        BigDecimal couponPrice = BigDecimal.ZERO;
        if (param.getCouponId() != null && param.getCouponId() > 0) {
            // 获取用户优惠券信息
            List<UserCoupon> userCoupons = userCouponService.getUserCoupons(userInfo.getId(), "available", 1, 100);
            UserCoupon selectedCoupon = userCoupons.stream()
                    .filter(coupon -> coupon.getId().equals(param.getCouponId()))
                    .findFirst()
                    .orElse(null);

            if (selectedCoupon != null) {
                // 检查优惠券是否满足使用条件
                if (selectedCoupon.getMinAmount().compareTo(goodsTotalPrice) <= 0) {
                    couponPrice = selectedCoupon.getAmount();
                    log.info("单商品订单使用优惠券：{}, 抵扣金额：{}", selectedCoupon.getTitle(), couponPrice);
                } else {
                    log.warn("优惠券{}不满足使用条件，最低消费：{}，订单金额：{}",
                            selectedCoupon.getTitle(), selectedCoupon.getMinAmount(), goodsTotalPrice);
                    throw new WeshopWechatException(WeshopWechatResultStatus.COUPON_USE_FAILED);
                }
            } else {
                log.warn("未找到可用的优惠券，ID：{}", param.getCouponId());
                throw new WeshopWechatException(WeshopWechatResultStatus.COUPON_USE_FAILED);
            }
        }

        // 计算积分抵扣
        Integer usePoints = param.getUsePoints();
        BigDecimal pointsPrice = BigDecimal.ZERO;
        if (usePoints != null && usePoints > 0) {
            // 验证用户积分是否足够（使用实时计算的积分）
            Integer userPoints = pointsService.getUserRealTimePoints(userInfo.getId());
            if (userPoints < usePoints) {
                log.warn("用户{}积分不足：当前积分={}, 尝试使用={}", userInfo.getId(), userPoints, usePoints);
                throw new WeshopWechatException(WeshopWechatResultStatus.INSUFFICIENT_POINTS);
            }
            log.debug("用户{}积分验证通过：当前积分={}, 使用积分={}", userInfo.getId(), userPoints, usePoints);

            // 计算积分可抵扣的金额
            pointsPrice = pointsService.calculatePointsValue(usePoints);
        }

        // 计算余额抵扣
        BigDecimal useBalance = param.getUseBalance();
        BigDecimal balancePrice = BigDecimal.ZERO;
        if (useBalance != null && useBalance.compareTo(BigDecimal.ZERO) > 0) {
            // 验证用户余额是否足够
            User currentUser = userService.queryById(userInfo.getId());
            BigDecimal userBalance = currentUser.getBalance() != null ? currentUser.getBalance() : BigDecimal.ZERO;
            if (userBalance.compareTo(useBalance) < 0) {
                throw new WeshopWechatException(WeshopWechatResultStatus.INSUFFICIENT_BALANCE);
            }
            balancePrice = useBalance;
        }

        // 订单价格计算  实际价格 = 商品价格 + 运费价格 - 优惠券价格 - 积分抵扣 - 余额抵扣
        BigDecimal orderTotalPrice = goodsTotalPrice.add(freightPrice).subtract(couponPrice);
        BigDecimal actualPrice = orderTotalPrice.subtract(pointsPrice).subtract(balancePrice);

        // 确保实际支付金额不为负数
        if (actualPrice.compareTo(BigDecimal.ZERO) < 0) {
            actualPrice = BigDecimal.ZERO;
        }
        Date currentTime = new Date();

        Order orderInfo = new Order();
        orderInfo.setOrderSn(IdGenerator.INSTANCE.nextId());
        orderInfo.setUserId(userInfo.getId());

        // 收货地址和运费
        orderInfo.setConsignee(checkedAddress.getName());
        orderInfo.setMobile(checkedAddress.getMobile());
        orderInfo.setProvince(checkedAddress.getProvinceId());
        orderInfo.setCity(checkedAddress.getCityId());
        orderInfo.setDistrict(checkedAddress.getDistrictId());
        orderInfo.setAddress(checkedAddress.getAddress());
        orderInfo.setFreightPrice(freightPrice);

        // 留言
        orderInfo.setPostscript(param.getPostscript());

        // 使用优惠券
        orderInfo.setCouponId(param.getCouponId() != null ? param.getCouponId() : 0);
        orderInfo.setCouponPrice(couponPrice);

        // 使用积分
        orderInfo.setIntegral(usePoints != null ? usePoints : 0);
        orderInfo.setIntegralMoney(pointsPrice);

        // 使用余额
        orderInfo.setBalancePrice(balancePrice);

        orderInfo.setCreateTime(currentTime);
        orderInfo.setGoodsPrice(goodsTotalPrice);
        orderInfo.setOrderPrice(orderTotalPrice);
        orderInfo.setActualPrice(BigDecimal.ZERO);
        if (Integer.valueOf(1).equals(param.getType())) {
            orderInfo.setActualPrice(actualPrice);
            orderInfo.setCouponId(0);

            // 根据实际支付金额设置订单状态
            if (actualPrice.compareTo(BigDecimal.ZERO) <= 0) {
                // 0元订单直接设置为已支付状态，并设置支付时间
                orderInfo.setOrderStatus(OrderStatusEnum.WAIT_SEND);
                orderInfo.setPayStatus(PayStatusEnum.PAID);
                orderInfo.setPayTime(currentTime);  // 设置支付时间为当前时间
                log.info("创建0元单商品订单，直接设置为已支付状态，订单号：{}，支付时间：{}", orderInfo.getOrderSn(), currentTime);
            } else {
                // 正常订单设置为待支付状态
                orderInfo.setOrderStatus(OrderStatusEnum.WAIT_PAY);
                orderInfo.setPayStatus(PayStatusEnum.PENDING_PAYMENT);
            }
        } else {
            // 礼券兑换订单（type=0），直接设置为已支付状态并设置支付时间
            orderInfo.setOrderStatus(OrderStatusEnum.WAIT_SEND);
            orderInfo.setPayStatus(PayStatusEnum.PAID);
            orderInfo.setPayTime(currentTime);  // 设置支付时间为当前时间
            log.info("创建礼券兑换订单，直接设置为已支付状态，订单号：{}，支付时间：{}", orderInfo.getOrderSn(), currentTime);
        }


        try {
            create(orderInfo);
        } catch (Exception e) {
            log.error("创建卡券订单失败: {}", e.getMessage(), e);
            throw new WeshopWechatException(WeshopWechatResultStatus.CREATE_ORDER_ERROR);
        }

        Order order = queryOneByCriteria(Criteria.of(Order.class).andEqualTo(Order::getOrderSn, orderInfo.getOrderSn()));
        if (order == null) {
            throw new WeshopWechatException(WeshopWechatResultStatus.CREATE_ORDER_ERROR);
        }

        // 更新卡券状态
        Card card = cardService.getById(param.getCouponId());
        if (card != null) {
            card.setStatus(1);
            cardService.updateById(card);
        }

        // 统计商品总价
        List<OrderGoods> orderGoodsList = new LinkedList<>();
        for (Cart goodsItem : checkedGoodsList) {
            OrderGoods orderGoods = new OrderGoods();
            orderGoods.setOrderId(order.getId());
            orderGoods.setGoodsId(goodsItem.getGoodsId());
            orderGoods.setGoodsSn(goodsItem.getGoodsSn());
            orderGoods.setProductId(goodsItem.getProductId());
            orderGoods.setGoodsName(goodsItem.getGoodsName());
            orderGoods.setListPicUrl(goodsItem.getListPicUrl());
            orderGoods.setMarketPrice(goodsItem.getMarketPrice());
            orderGoods.setRetailPrice(goodsItem.getRetailPrice());
            orderGoods.setNumber(goodsItem.getNumber());
            orderGoods.setGoodsSpecificationNameValue(goodsItem.getGoodsSpecificationNameValue());
            orderGoods.setGoodsSpecificationIds(goodsItem.getGoodsSpecificationIds());
            orderGoods.setIsReal(true);
            orderGoodsList.add(orderGoods);
        }
        Optional.ofNullable(orderGoodsService.createBatch(orderGoodsList)).orElseThrow(() ->
                new WeshopWechatException(WeshopWechatResultStatus.CREATE_ORDER_ERROR));

        // 订单商品创建完成后，设置推广者信息（支持阶梯推广）
        setPromotionInfoWithGoods(order, userInfo, orderGoodsList, param.getPromoterId());

        // 使用优惠券
        if (param.getCouponId() != null && param.getCouponId() > 0) {
            boolean couponUsed = userCouponService.useCoupon(
                    userInfo.getId(),
                    param.getCouponId(),
                    order.getId()
            );
            if (!couponUsed) {
                throw new WeshopWechatException(WeshopWechatResultStatus.COUPON_USE_FAILED);
            }
            log.info("单商品订单{}成功使用优惠券{}", order.getId(), param.getCouponId());
        }

        // 使用积分抵扣
        if (usePoints != null && usePoints > 0) {
            try {
                boolean pointsUsed = pointsService.usePointsForOrder(
                        userInfo.getId(),
                        order.getId(),
                        usePoints,
                        "订单使用积分抵扣"
                );
                if (!pointsUsed) {
                    throw new WeshopWechatException(WeshopWechatResultStatus.POINTS_USE_FAILED);
                }
            } catch (RuntimeException e) {
                log.error("订单{}积分扣减失败：{}", order.getId(), e.getMessage());
                throw new WeshopWechatException(WeshopWechatResultStatus.POINTS_USE_FAILED);
            }
        }

        // 使用余额支付
        if (balancePrice.compareTo(BigDecimal.ZERO) > 0) {
            boolean balanceUsed = balanceService.useBalanceForOrder(
                    userInfo.getId(),
                    balancePrice,
                    order.getId(),
                    "订单使用余额支付"
            );
            if (!balanceUsed) {
                throw new WeshopWechatException(WeshopWechatResultStatus.BALANCE_USE_FAILED);
            }
        }

        // 发送微信订阅消息通知
        //sendOrderSubscribeNotification(order, userInfo, orderGoodsList);

        // 向运维人员发送订单通知
        //sendAdminNotification(order, userInfo, orderGoodsList, true);

        return new OrderSubmitResultVO(order);
    }

    /**
     * 发送订单订阅消息通知
     * 使用微信订阅消息，不受48小时限制，但需要用户授权订阅
     *
     * @param order          订单信息
     * @param userInfo       用户基本信息
     * @param orderGoodsList 订单商品列表
     */
    private void sendOrderSubscribeNotification(Order order, User userInfo, List<OrderGoods> orderGoodsList) {
        try {
            // 获取完整的用户信息，确保包含微信OpenID
            User user = userService.queryById(userInfo.getId());
            if (user == null || user.getWechatOpenId() == null || user.getWechatOpenId().isEmpty()) {
                log.warn("用户信息不完整或无微信OpenID，无法发送通知，userId: {}, orderId: {}",
                        userInfo.getId(), order.getId());
                return;
            }

            // 确定是否为卡券订单
            boolean isCardOrder = order.getCouponId() != null && order.getCouponId() > 0;

            // 发送订阅消息通知
            boolean success = wxMessageService.sendOrderSubscribeMessage(order, user, orderGoodsList, isCardOrder);
            if (success) {
                log.info("订单订阅通知发送成功，订单ID: {}, 用户ID: {}", order.getId(), user.getId());
            } else {
                // 可能是由于用户未订阅或其他原因导致的发送失败
                log.warn("订单订阅通知发送失败，可能是用户未授权订阅，订单ID: {}, 用户ID: {}", order.getId(), user.getId());

                // 尝试使用客服消息作为备选方案
                try {
                    boolean kefuSuccess = wxMessageService.sendOrderNotice(order, user, orderGoodsList, isCardOrder);
                    if (kefuSuccess) {
                        log.info("订单客服通知发送成功（订阅消息失败后的备选方案），订单ID: {}, 用户ID: {}", order.getId(), user.getId());
                    }
                } catch (Exception e) {
                    log.warn("订阅消息和客服消息都发送失败，订单ID: {}, 用户ID: {}", order.getId(), user.getId());
                }
            }
        } catch (Exception e) {
            // 消息发送失败不影响订单创建，只记录日志
            log.error("发送订单订阅通知失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 向运维人员发送订单通知消息
     *
     * @param order          订单信息
     * @param userInfo       用户基本信息
     * @param orderGoodsList 订单商品列表
     * @param isCardOrder    是否为卡券订单
     */
    private void sendAdminNotification(Order order, User userInfo, List<OrderGoods> orderGoodsList, boolean isCardOrder) {
        try {
            // 获取完整的用户信息
            User user = userService.queryById(userInfo.getId());

            // 发送订单通知给运维人员
            boolean success = adminNotificationService.notifyOrderCreation(order, user, orderGoodsList, isCardOrder);
            if (success) {
                log.info("向运维人员发送订单通知成功，订单ID: {}", order.getId());
            } else {
                log.warn("向运维人员发送订单通知失败，订单ID: {}", order.getId());
            }
        } catch (Exception e) {
            // 管理员通知发送失败不影响订单创建，只记录日志
            log.error("向运维人员发送订单通知失败: {}", e.getMessage(), e);
        }
    }

    @Transactional
    public Boolean cancel(Integer orderId) {
        Order order = queryById(orderId);
        if (order == null) {
            log.warn("订单不存在，orderId: {}", orderId);
            return false;
        }

        // 只有待付款状态的订单才能取消
        if (order.getOrderStatus() != OrderStatusEnum.WAIT_PAY) {
            log.warn("订单状态不允许取消，orderId: {}, status: {}", orderId, order.getOrderStatus());
            return false;
        }

        try {
            // 1. 更新订单状态
            order.setOrderStatus(OrderStatusEnum.CANCELLED);
            boolean orderUpdated = updateNotNull(order) == 1;
            if (!orderUpdated) {
                log.error("更新订单状态失败，orderId: {}", orderId);
                return false;
            }

            // 2. 退回优惠券
            if (order.getCouponId() != null && order.getCouponId() > 0) {

            }

            boolean couponRefunded = userCouponService.refundCoupon(orderId);
            if (!couponRefunded) {
                log.error("退回优惠券失败，orderId: {}", orderId);
                // 不抛异常，继续执行其他退回操作
            }

            // 3. 退回积分
            if (order.getIntegral() != null && order.getIntegral() > 0) {
                log.info("订单{}使用了{}积分，开始退回积分", orderId, order.getIntegral());
                boolean pointsRefunded = pointsService.refundPointsFromOrder(order.getUserId(), orderId);
                if (!pointsRefunded) {
                    log.error("退回积分失败，orderId: {}, userId: {}, integral: {}", orderId, order.getUserId(), order.getIntegral());
                    // 不抛异常，继续执行其他退回操作
                } else {

                    log.info("订单{}积分退回成功", orderId);
                }
            } else {
                log.info("订单{}未使用积分，跳过积分退回，integral: {}", orderId, order.getIntegral());
            }

            // 4. 退回余额
            if (order.getBalancePrice() != null && order.getBalancePrice().compareTo(BigDecimal.ZERO) > 0) {
                boolean balanceRefunded = balanceService.refundBalanceFromOrder(order.getUserId(), orderId);
                if (!balanceRefunded) {
                    log.error("退回余额失败，orderId: {}", orderId);
                    // 不抛异常，继续执行其他退回操作
                }
            }

            // 5. 取消推广收益
            try {
                promotionEarningsService.cancelEarnings(orderId);
            } catch (Exception e) {
                log.error("取消推广收益失败，orderId: {}", orderId, e);
                // 不抛异常，不影响订单取消
            }

            log.info("订单取消成功，orderId: {}, userId: {}", orderId, order.getUserId());
            return true;

        } catch (Exception e) {
            log.error("取消订单失败，orderId: {}", orderId, e);
            throw new RuntimeException("取消订单失败：" + e.getMessage());
        }
    }

    public Boolean confirm(Integer orderId) {
        Order order = queryById(orderId);
        order.setOrderStatus(OrderStatusEnum.COMPLETED);
        order.setConfirmReceiveTime(new Date()); // 设置确认收货时间
        boolean updated = updateNotNull(order) == 1;

        // 订单完成后给用户增加积分
        /*if (updated) {
            try {
                pointsService.earnPointsFromOrder(order.getUserId(), order.getId(), order.getGoodsPrice());
                log.info("订单{}完成，用户{}获得积分", orderId, order.getUserId());
            } catch (Exception e) {
                log.error("订单{}完成后增加积分失败: {}", orderId, e.getMessage(), e);
            }
        }*/

        // 订单确认完成后，处理推广收益状态更新
        if (updated) {
            try {
                // 这里应该调用PaymentEventHandler来处理确认收货事件
                // 但由于依赖注入循环问题，我们直接调用PromotionCommissionService的方法
                promotionEarningsService.confirmEarnings(orderId);
                log.info("订单{}确认收货，推广收益状态已更新", orderId);
            } catch (Exception e) {
                log.error("订单{}确认收货后更新推广收益状态失败: {}", orderId, e.getMessage(), e);
            }
        }

        return updated;
    }

    /**
     * 管理员发货操作
     *
     * @param orderId        订单ID
     * @param expressType    快递类型
     * @param expressCode    快递编码
     * @param trackingNumber 快递单号
     * @return 发货是否成功
     */
    @Transactional
    public boolean deliverOrder(Integer orderId, String expressType, String expressCode, String trackingNumber) {
        try {
            // 1. 查询订单信息
            Order order = queryById(orderId);
            if (order == null) {
                log.error("订单不存在，orderId: {}", orderId);
                return false;
            }

            // 2. 检查订单状态是否可以发货
            if (!OrderStatusEnum.WAIT_SEND.equals(order.getOrderStatus())) {
                log.error("订单状态不允许发货，orderId: {}, status: {}", orderId, order.getOrderStatus());
                return false;
            }

            // 3. 更新订单状态为已发货
            order.setOrderStatus(OrderStatusEnum.WAIT_RECEIVE);
            order.setShippingInfoStatus(1); // 已发货
            order.setShippingTime(new Date());
            boolean orderUpdated = updateNotNull(order) == 1;

            if (!orderUpdated) {
                log.error("更新订单状态失败，orderId: {}", orderId);
                return false;
            }

            // 4. 保存快递信息
            OrderExpress orderExpress = OrderExpress.builder()
                    .orderId(orderId)
                    .shipperName(expressType)
                    .shipperCode(expressCode)
                    .logisticCode(trackingNumber)
                    .isFinish(false)
                    .requestCount(0)
                    .createTime(new Date())
                    .updateTime(new Date())
                    .logisticsStatus("已发货")
                    .build();

            boolean expressCreated = orderExpressService.create(orderExpress) == 1;
            if (!expressCreated) {
                log.error("保存快递信息失败，orderId: {}", orderId);
                return false;
            }

            log.info("订单发货成功，orderId: {}, 快递公司: {}, 快递单号: {}",
                    orderId, expressType, trackingNumber);
            return true;

        } catch (Exception e) {
            log.error("发货操作失败，orderId: {}", orderId, e);
            return false;
        }
    }

    /**
     * 设置订单的推广者信息（仅记录推广者ID，佣金计算在支付成功后进行）
     *
     * @param order              订单对象
     * @param user               下单用户
     * @param orderGoodsList     订单商品列表
     * @param frontendPromoterId 前端传递的推广者ID（优先级最高）
     */
    private void setPromotionInfoWithGoods(Order order, User user, List<OrderGoods> orderGoodsList, Integer frontendPromoterId) {
        try {
            Integer finalPromoterId = null;
            String promoterSource = "";

            // 优先级1：前端传递的推广者ID（最高优先级）
            if (frontendPromoterId != null && frontendPromoterId > 0) {
                finalPromoterId = frontendPromoterId;
                promoterSource = "前端传递";
                log.info("订单{}使用前端传递的推广者ID：{}", order.getOrderSn(), frontendPromoterId);
            }
            // 优先级2：用户表中的推广者ID（兜底方案）
            else if (user.getPromoterId() != null) {
                finalPromoterId = user.getPromoterId();
                promoterSource = "用户推广关系";
                log.info("订单{}使用用户推广关系中的推广者ID：{}", order.getOrderSn(), user.getPromoterId());
            }

            if (finalPromoterId != null) {
                // 验证推广者是否存在
                User promoter = userMapper.selectById(finalPromoterId);
                if (promoter == null) {
                    log.warn("推广者{}不存在，跳过设置推广关系", finalPromoterId);
                    return;
                }

                // 检查是否自己推广自己
                if (finalPromoterId.equals(user.getId())) {
                    log.warn("用户{}不能推广自己，跳过设置推广关系", user.getId());
                    return;
                }

                // 设置推广者ID
                order.setPromoterId(finalPromoterId);

                // 更新订单到数据库
                int updateResult = updateById(order);

                if (updateResult > 0) {
                    log.info("订单{}推广者设置成功：推广者ID={}，来源={}，佣金将在支付成功后计算",
                            order.getOrderSn(), finalPromoterId, promoterSource);
                } else {
                    log.error("订单{}推广者设置失败：数据库更新失败", order.getOrderSn());
                }
            } else {
                log.info("订单{}无推广者信息，跳过推广关系设置", order.getOrderSn());
            }
        } catch (Exception e) {
            log.error("设置订单推广者信息失败，订单号：{}", order.getOrderSn(), e);
            // 不抛出异常，避免影响订单创建流程
        }
    }

    /**
     * 兼容旧版本的方法（无前端推广者ID参数）
     */
    private void setPromotionInfoWithGoods(Order order, User user, List<OrderGoods> orderGoodsList) {
        setPromotionInfoWithGoods(order, user, orderGoodsList, null);
    }
}
